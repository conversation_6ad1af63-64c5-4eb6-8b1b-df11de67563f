# لعبة الدودة - Snake Game

لعبة الدودة الكلاسيكية مطورة بلغة Python باستخدام مكتبة pygame.

## المتطلبات

- Python 3.7 أو أحدث
- مك<PERSON><PERSON>ة pygame

## التثبيت

1. تأكد من تثبيت Python على جهازك
2. قم بتثبيت المكتبات المطلوبة:

```bash
pip install -r requirements.txt
```

أو:

```bash
pip install pygame
```

## تشغيل اللعبة

```bash
python snake_game.py
```

## طريقة اللعب

- استخدم أسهم لوحة المفاتيح للتحكم في اتجاه الدودة:
  - ↑ للأعلى
  - ↓ للأسفل  
  - ← لليسار
  - → لليمين

- الهدف هو أكل التفاح الأحمر لزيادة طول الدودة والنقاط
- تجنب الاصطدام بالحدود أو بجسم الدودة نفسها
- النقاط تظهر في الزاوية اليمنى السفلى

## الميزات

- رسومات ملونة وجذابة
- نظام نقاط
- حركة سلسة للدودة
- خلفية عشبية جميلة
- منع الدودة من العكس في الاتجاه المعاكس مباشرة

## التحكم

- الدودة تتحرك تلقائياً في الاتجاه المحدد
- لا يمكن تغيير الاتجاه إلى الاتجاه المعاكس مباشرة
- اللعبة تنتهي عند الاصطدام بالحدود أو بالجسم

استمتع باللعب! 🐍🍎
