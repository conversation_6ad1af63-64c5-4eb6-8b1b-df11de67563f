# -*- coding: utf-8 -*-
import tkinter as tk
import random

class SnakeGame:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("لعبة الدودة - Snake Game")
        self.window.resizable(False, False)
        
        # إعدادات اللعبة
        self.board_width = 600
        self.board_height = 600
        self.unit_size = 25
        
        self.canvas = tk.Canvas(self.window, bg="black", width=self.board_width, height=self.board_height)
        self.canvas.pack()
        
        self.window.update()
        
        # وضع النافذة في المنتصف
        window_width = self.window.winfo_width()
        window_height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        
        x = int((screen_width/2) - (window_width/2))
        y = int((screen_height/2) - (window_height/2))
        
        self.window.geometry("{}x{}+{}+{}".format(window_width, window_height, x, y))
        
        # متغيرات اللعبة
        self.snake = []
        self.food = {}
        self.direction = 'Right'
        self.score = 0
        self.game_over = False
        
        # ربط الأحداث
        self.window.bind('<Key>', self.change_direction)
        self.window.focus_set()
        
        # بدء اللعبة
        self.new_game()
        
    def new_game(self):
        # إعادة تعيين المتغيرات
        self.score = 0
        self.direction = 'Right'
        self.game_over = False
        
        # إنشاء الدودة
        self.snake = []
        for i in range(3):
            self.snake.append([0, 0])
            
        # إنشاء الطعام
        self.new_food()
        
        # تحديث النتيجة
        self.update_score()
        
        # بدء حلقة اللعبة
        self.next_turn()
        
    def next_turn(self):
        if not self.game_over:
            x, y = self.snake[0]
            
            if self.direction == "Up":
                y -= self.unit_size
            elif self.direction == "Down":
                y += self.unit_size
            elif self.direction == "Left":
                x -= self.unit_size
            elif self.direction == "Right":
                x += self.unit_size
                
            self.snake.insert(0, [x, y])
            
            # التحقق من أكل الطعام
            if x == self.food['x'] and y == self.food['y']:
                self.score += 1
                self.update_score()
                self.new_food()
            else:
                del self.snake[-1]
                
            # التحقق من التصادم
            if self.check_collisions():
                self.game_over = True
                
            self.update_canvas()
            
            if not self.game_over:
                self.window.after(100, self.next_turn)
            else:
                self.display_game_over()
                
    def new_food(self):
        x = random.randint(0, (self.board_width / self.unit_size) - 1) * self.unit_size
        y = random.randint(0, (self.board_height / self.unit_size) - 1) * self.unit_size
        
        self.food = {'x': x, 'y': y}
        
    def update_canvas(self):
        self.canvas.delete("all")
        
        # رسم الطعام
        self.canvas.create_oval(self.food['x'], self.food['y'], 
                               self.food['x'] + self.unit_size, self.food['y'] + self.unit_size, 
                               fill='red', tags="food")
        
        # رسم الدودة
        for segment in self.snake:
            self.canvas.create_rectangle(segment[0], segment[1], 
                                       segment[0] + self.unit_size, segment[1] + self.unit_size, 
                                       fill='green', tags="snake")
            
    def change_direction(self, event):
        new_direction = event.keysym
        
        if new_direction == 'Up':
            if self.direction != 'Down':
                self.direction = new_direction
        elif new_direction == 'Down':
            if self.direction != 'Up':
                self.direction = new_direction
        elif new_direction == 'Left':
            if self.direction != 'Right':
                self.direction = new_direction
        elif new_direction == 'Right':
            if self.direction != 'Left':
                self.direction = new_direction
                
    def check_collisions(self):
        x, y = self.snake[0]
        
        # التحقق من الحدود
        if x < 0 or x >= self.board_width or y < 0 or y >= self.board_height:
            return True
            
        # التحقق من التصادم مع الجسم
        for body_part in self.snake[1:]:
            if x == body_part[0] and y == body_part[1]:
                return True
                
        return False
        
    def display_game_over(self):
        self.canvas.create_text(self.canvas.winfo_width()/2, self.canvas.winfo_height()/2,
                               font=('Arial', 30), text="GAME OVER", fill="red", tags="gameover")
        self.canvas.create_text(self.canvas.winfo_width()/2, self.canvas.winfo_height()/2 + 50,
                               font=('Arial', 20), text="النتيجة النهائية: {}".format(self.score), fill="white", tags="gameover")
        self.canvas.create_text(self.canvas.winfo_width()/2, self.canvas.winfo_height()/2 + 100,
                               font=('Arial', 16), text="اضغط Space لبدء لعبة جديدة", fill="yellow", tags="gameover")
        
        self.window.bind('<space>', lambda event: self.restart_game())
        
    def restart_game(self):
        self.canvas.delete("gameover")
        self.new_game()
        
    def update_score(self):
        self.window.title("لعبة الدودة - النتيجة: {}".format(self.score))
        
    def run(self):
        self.window.mainloop()

if __name__ == "__main__":
    game = SnakeGame()
    game.run()
